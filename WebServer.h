#ifndef WEB_SERVER_H
#define WEB_SERVER_H

#include <Arduino.h>
#include <WiFi.h>
#include "RobotConfig.h"

// ============================================================================
// SERVIDOR WEB PARA CONTROLE E MONITORAMENTO DO ROBÔ
// ============================================================================

class WebServer {
private:
  WiFiServer server;
  StatusData* statusData;
  
  // Métodos privados para respostas HTTP
  void sendHTMLPage(WiFiClient &client);
  void sendStatusResponse(WiFiClient &client);
  void sendSimpleResponse(WiFiClient &client, String message);
  void send404Response(WiFiClient &client);
  
  // Processamento de comandos
  bool processParameterUpdate(String request);
  bool processCalibrationCommand();
  bool processStartCommand();
  bool processStopCommand();

public:
  WebServer();
  
  // Inicialização
  void begin();
  void stop();
  
  // Configuração de dados
  void setStatusData(StatusData* data);
  
  // Processamento de requisições
  void handleRequests();
  
  // Status
  bool isRunning();
  
  // Callbacks para comandos (definidos externamente)
  void (*onCalibrate)() = nullptr;
  void (*onStart)() = nullptr;
  void (*onStop)() = nullptr;
  void (*onParameterUpdate)(float kp, float ki, float kd, int speed) = nullptr;
};

#endif // WEB_SERVER_H
