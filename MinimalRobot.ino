// ============================================================================
// VERSÃO MÍNIMA - ROBÔ SEGUIDOR DE LINHA MODULAR
// ============================================================================
// Esta versão inclui apenas os módulos essenciais para funcionamento básico
// Use esta versão se houver problemas de compilação com a versão completa

#include <QTRSensors.h>
#include "RobotConfig.h"
#include "SensorManager.h"
#include "MotorController.h"
#include "PIDController.h"

// Instâncias dos módulos essenciais
SensorManager sensorManager;
MotorController motorController;
PIDController pidController;

// Estado simples
enum SimpleState {
  SIMPLE_CALIBRATION,
  SIMPLE_RUNNING,
  SIMPLE_STOPPED
};

SimpleState currentState = SIMPLE_CALIBRATION;
bool calibrationDone = false;

void setup() {
  Serial.begin(115200);
  delay(2000);

  Serial.println("=== ROBÔ SEGUIDOR DE LINHA - VERSÃO MÍNIMA ===");
  Serial.println("Inicializando módulos essenciais...");

  // Inicializar módulos
  sensorManager.begin();
  motorController.begin();
  pidController.begin();

  Serial.println("Módulos inicializados!");
  Serial.println("Iniciando calibração em 3 segundos...");
  Serial.println("Mova o robô sobre a linha durante a calibração!");
  
  delay(3000);
  startCalibration();
}

void loop() {
  switch (currentState) {
    case SIMPLE_CALIBRATION:
      handleCalibration();
      break;
      
    case SIMPLE_RUNNING:
      handleRunning();
      break;
      
    case SIMPLE_STOPPED:
      handleStopped();
      break;
  }
  
  // Processar comandos seriais simples
  handleSerialCommands();
  
  delay(10); // Pequeno delay para estabilidade
}

void startCalibration() {
  Serial.println("=== INICIANDO CALIBRAÇÃO ===");
  Serial.println("Mova o robô sobre a linha por 10 segundos...");
  
  digitalWrite(LED_BUILTIN, HIGH);
  
  // Calibração por tempo (10 segundos)
  unsigned long startTime = millis();
  while (millis() - startTime < 10000) {
    sensorManager.updateReadings();
    
    // Feedback visual
    if ((millis() - startTime) % 1000 == 0) {
      Serial.print("Calibrando... ");
      Serial.print((millis() - startTime) / 1000);
      Serial.println("s");
    }
    
    delay(20);
  }
  
  digitalWrite(LED_BUILTIN, LOW);
  calibrationDone = true;
  currentState = SIMPLE_RUNNING;
  
  Serial.println("Calibração completa!");
  Serial.println("Robô pronto para seguir linha!");
  Serial.println("Comandos: 's' = parar, 'r' = reiniciar, 'c' = calibrar");
}

void handleCalibration() {
  // Aguardar calibração manual
  if (!calibrationDone) {
    static unsigned long lastBlink = 0;
    if (millis() - lastBlink >= 500) {
      digitalWrite(LED_BUILTIN, !digitalRead(LED_BUILTIN));
      lastBlink = millis();
    }
  }
}

void handleRunning() {
  // Atualizar sensores
  sensorManager.updateReadings();
  
  // Calcular erro da linha
  uint16_t linePosition = sensorManager.getLinePosition();
  int error = 3500 - linePosition; // Centro da linha
  
  // Verificar se perdeu a linha
  if (linePosition == 0 || linePosition == 7000) {
    static unsigned long lostTime = 0;
    if (lostTime == 0) {
      lostTime = millis();
    } else if (millis() - lostTime > 2000) {
      // Perdeu linha por 2 segundos - parar
      currentState = SIMPLE_STOPPED;
      Serial.println("Linha perdida - Parando");
      return;
    }
  } else {
    // Reset timer se encontrou linha
    static unsigned long lostTime = 0;
    lostTime = 0;
  }
  
  // Calcular PID e controlar motores
  pidController.calculate(error);
  motorController.setMotorSpeeds(
    pidController.getLeftSpeed(), 
    pidController.getRightSpeed()
  );
  
  // Status periódico
  static unsigned long lastStatus = 0;
  if (millis() - lastStatus >= 2000) {
    Serial.print("Posição: ");
    Serial.print(linePosition);
    Serial.print(" | Erro: ");
    Serial.print(error);
    Serial.print(" | Motores: E=");
    Serial.print(pidController.getLeftSpeed());
    Serial.print(" D=");
    Serial.println(pidController.getRightSpeed());
    lastStatus = millis();
  }
}

void handleStopped() {
  motorController.stopMotors();
  
  // LED fixo
  digitalWrite(LED_BUILTIN, HIGH);
  
  // Verificar se encontrou linha novamente
  sensorManager.updateReadings();
  uint16_t linePosition = sensorManager.getLinePosition();
  
  if (linePosition > 100 && linePosition < 6900) {
    currentState = SIMPLE_RUNNING;
    pidController.reset();
    digitalWrite(LED_BUILTIN, LOW);
    Serial.println("Linha encontrada - Reiniciando");
  }
}

void handleSerialCommands() {
  if (Serial.available()) {
    char command = Serial.read();
    
    switch (command) {
      case 's':
      case 'S':
        currentState = SIMPLE_STOPPED;
        Serial.println("Comando: Parar");
        break;
        
      case 'r':
      case 'R':
        currentState = SIMPLE_RUNNING;
        pidController.reset();
        Serial.println("Comando: Reiniciar");
        break;
        
      case 'c':
      case 'C':
        calibrationDone = false;
        currentState = SIMPLE_CALIBRATION;
        Serial.println("Comando: Recalibrar");
        delay(1000);
        startCalibration();
        break;
        
      case 't':
      case 'T':
        testSystem();
        break;
        
      case 'h':
      case 'H':
        printHelp();
        break;
        
      default:
        if (command != '\n' && command != '\r') {
          Serial.println("Comando inválido. Digite 'h' para ajuda.");
        }
        break;
    }
  }
}

void testSystem() {
  Serial.println("=== TESTE DO SISTEMA ===");
  
  // Teste dos sensores
  sensorManager.updateReadings();
  Serial.print("Posição da linha: ");
  Serial.println(sensorManager.getLinePosition());
  Serial.print("Sensor 1: ");
  Serial.println(sensorManager.getStateSensor1());
  Serial.print("Sensor 2: ");
  Serial.println(sensorManager.getStateSensor2());
  
  // Teste dos motores
  Serial.println("Testando motores...");
  motorController.testMotors();
  
  Serial.println("Teste concluído!");
}

void printHelp() {
  Serial.println("=== COMANDOS DISPONÍVEIS ===");
  Serial.println("s - Parar robô");
  Serial.println("r - Reiniciar movimento");
  Serial.println("c - Recalibrar sensores");
  Serial.println("t - Testar sistema");
  Serial.println("h - Mostrar esta ajuda");
  Serial.println("============================");
}
