# Robô Seguidor de Linha - Versão Modular

## Visão Geral

Este projeto implementa um robô seguidor de linha para Raspberry Pi Pico W usando uma arquitetura modular bem estruturada. O código foi refatorado de uma implementação monolítica para um sistema modular que facilita manutenção, depuração e extensibilidade.

## Arquitetura Modular

### Módulos Principais

#### 1. **RobotConfig.h**
- Configurações centralizadas do sistema
- Definições de pinos, constantes PID, parâmetros WiFi
- Estruturas de dados compartilhadas
- Enumerações de estados

#### 2. **SensorManager** (.h/.cpp)
- Gerenciamento dos sensores QTR (linha)
- Controle dos sensores de estado (transições)
- Calibração automática dos sensores
- Leitura e processamento de dados

#### 3. **MotorController** (.h/.cpp)
- Controle PWM dos motores
- Compensação de zona morta
- Limitação de velocidade
- Funções de teste dos motores

#### 4. **PIDController** (.h/.cpp)
- Controlador PID dual (reta/curva)
- Parâmetros configuráveis por estado
- Prevenção de integral windup
- Cálculo de velocidades dos motores

#### 5. **StateMachine** (.h/.cpp)
- Máquina de estados do robô
- Detecção de transições
- Controle de fluxo de execução
- Gerenciamento de contadores

#### 6. **WiFiManager** (.h/.cpp)
- Conexão WiFi em modo cliente
- Monitoramento e reconexão automática
- Informações de rede
- Controle de energia

#### 7. **WebServer** (.h/.cpp)
- Servidor HTTP para controle remoto
- Interface web responsiva
- Callbacks para comandos
- Processamento de parâmetros

#### 8. **PerformanceMonitor** (.h/.cpp)
- Monitoramento de performance do sistema
- Cálculo de frequência de loop
- Métricas de timing
- Relatórios de performance

## Funcionalidades

### Estados do Sistema
- **CALIBRATION**: Calibração inicial dos sensores
- **WAITING_COMMAND**: Aguardando comando via web
- **INITIAL**: Estado inicial antes do movimento
- **STRAIGHT**: Movimento em linha reta
- **CURVE**: Movimento em curva
- **STOPPED**: Robô parado

### Controle Dual PID
- Parâmetros separados para reta e curva
- Ajuste automático baseado no estado
- Reset automático em transições

### Interface Web
- Controle remoto via browser
- Monitoramento em tempo real
- Ajuste de parâmetros online
- Auto-refresh de status

### Comandos Seriais
- `status` - Status completo do sistema
- `test` - Teste de todos os módulos
- `calibrate` - Iniciar calibração
- `start` - Iniciar movimento
- `stop` - Parar robô
- `wifi` - Informações WiFi
- `performance` - Relatório de performance
- `help` - Ajuda dos comandos

## Configuração

### Hardware
- **Plataforma**: Raspberry Pi Pico W
- **Board Package**: earlephilhower/arduino-pico
- **Sensores**: 8x QTR + 2x sensores de estado
- **Motores**: 2x motores DC com driver

### Pinos (configuráveis em RobotConfig.h)
```cpp
// Sensores QTR
QTR_SENSOR_PINS: {1, 2, 3, 4, 5, 6, 7, 8}
QTR_EMITTER_PIN: 0

// Motores
MOTOR_EN_A: 15, MOTOR_IN1_A: 14
MOTOR_EN_B: 17, MOTOR_IN1_B: 16

// Sensores de Estado
STATE_SENSOR1: 26, STATE_SENSOR2: 27
```

### WiFi
```cpp
WIFI_SSID: "Sua_Rede_WiFi"
WIFI_PASSWORD: "Sua_Senha"
```

## Vantagens da Modularização

1. **Organização**: Código bem estruturado e legível
2. **Manutenibilidade**: Fácil localização e correção de problemas
3. **Reutilização**: Módulos podem ser reutilizados em outros projetos
4. **Testabilidade**: Cada módulo pode ser testado independentemente
5. **Escalabilidade**: Fácil adição de novas funcionalidades
6. **Separação de Responsabilidades**: Cada módulo tem uma função específica

## Como Usar

1. **Compilação**: Abra o projeto no Arduino IDE com o board package earlephilhower/arduino-pico
2. **Configuração**: Ajuste as configurações em RobotConfig.h conforme seu hardware
3. **Upload**: Carregue o código no Pico W
4. **Calibração**: O sistema inicia automaticamente em modo de calibração
5. **Controle**: Use a interface web ou comandos seriais para controlar o robô

## Bibliotecas Necessárias

- **QTRSensors**: Para os sensores de linha
- **WiFi**: Biblioteca padrão do Pico W (incluída no board package)

## Estrutura de Arquivos

```
Assemble_2PID_V2_Augment/
├── Assemble_2PID_V2_NoArduinoJson.ino  # Arquivo principal
├── RobotConfig.h                        # Configurações
├── SensorManager.h/.cpp                 # Gerenciador de sensores
├── MotorController.h/.cpp               # Controlador de motores
├── PIDController.h/.cpp                 # Controlador PID
├── StateMachine.h/.cpp                  # Máquina de estados
├── WiFiManager.h/.cpp                   # Gerenciador WiFi
├── WebServer.h/.cpp                     # Servidor web
├── PerformanceMonitor.h/.cpp            # Monitor de performance
└── README.md                            # Esta documentação
```

## Próximos Passos

- Implementar logging avançado
- Adicionar suporte a múltiplos perfis de configuração
- Implementar comunicação via Bluetooth
- Adicionar sensores adicionais (giroscópio, acelerômetro)
- Implementar algoritmos de navegação mais avançados
