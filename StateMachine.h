#ifndef STATE_MACHINE_H
#define STATE_MACHINE_H

#include "RobotConfig.h"

// ============================================================================
// MÁQUINA DE ESTADOS DO ROBÔ SEGUIDOR DE LINHA
// ============================================================================

class StateMachine {
private:
  RobotState currentState;
  String stateString;
  
  // Controle de transições
  bool condition1Previous;
  bool condition2Previous;
  bool condition2SensorPrevious;
  int transitionCount;
  int stopTransitionCount;
  
  // Flags de controle
  bool isCalibrating;
  
  // Métodos privados
  void updateStateString();
  void detectTransitions(int sensor1, int sensor2);

public:
  StateMachine();
  
  // Inicialização
  void begin();
  
  // Controle de estado
  void setState(RobotState newState);
  RobotState getCurrentState();
  String getStateString();
  
  // Processamento de transições
  void processTransitions(int sensor1, int sensor2);
  void changeState();
  
  // Controle de calibração
  void startCalibration();
  void finishCalibration();
  bool isInCalibration();
  
  // Acesso aos contadores
  int getTransitionCount();
  int getStopTransitionCount();
  void resetCounters();
  
  // Utilitários
  void printStateInfo();
  bool canStartMovement();
  bool shouldStopMovement();
};

#endif // STATE_MACHINE_H
