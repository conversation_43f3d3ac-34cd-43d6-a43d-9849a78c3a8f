#include "SensorManager.h"

// ============================================================================
// IMPLEMENTAÇÃO DO GERENCIADOR DE SENSORES
// ============================================================================

SensorManager::SensorManager() : isCalibrated(false) {
  // Inicializar dados dos sensores
  for (int i = 0; i < SENSOR_COUNT; i++) {
    sensorData.qtrValues[i] = 0;
  }
  sensorData.linePosition = 0;
  sensorData.stateSensor1 = 0;
  sensorData.stateSensor2 = 0;
  sensorData.qtrReadings = "";
}

void SensorManager::begin() {
  Serial.println("=== Inicializando Sensores ===");
  
  // Configurar sensores QTR
  qtr.setTypeRC();
  qtr.setSensorPins(QTR_SENSOR_PINS, SENSOR_COUNT);
  qtr.setEmitterPin(QTR_EMITTER_PIN);
  
  // Configurar pinos dos sensores de estado
  pinMode(STATE_SENSOR1, INPUT);
  pinMode(STATE_SENSOR2, INPUT);
  
  Serial.println("Sensores QTR configurados");
  Serial.print("Pinos QTR: ");
  for (int i = 0; i < SENSOR_COUNT; i++) {
    Serial.print(QTR_SENSOR_PINS[i]);
    if (i < SENSOR_COUNT - 1) Serial.print(", ");
  }
  Serial.println();
  Serial.println("Sensores de estado configurados");
  Serial.println("==============================");
}

void SensorManager::calibrate() {
  Serial.println("=== Iniciando Calibração dos Sensores QTR ===");
  
  digitalWrite(LED_BUILTIN, HIGH);
  
  // Calibração com 400 amostras
  for (uint16_t i = 0; i < 400; i++) {
    qtr.calibrate();
    delay(1);
    
    // Feedback visual durante calibração
    if (i % 100 == 0) {
      Serial.print("Progresso: ");
      Serial.print((i * 100) / 400);
      Serial.println("%");
    }
  }
  
  digitalWrite(LED_BUILTIN, LOW);
  isCalibrated = true;
  
  Serial.println("Calibração completa!");
  printCalibrationValues();
  Serial.println("==========================================");
}

bool SensorManager::isCalibrationComplete() {
  return isCalibrated;
}

void SensorManager::updateReadings() {
  // Ler sensores QTR
  sensorData.linePosition = qtr.readLineWhite(sensorData.qtrValues);
  
  // Ler sensores de estado
  sensorData.stateSensor1 = analogRead(STATE_SENSOR1);
  sensorData.stateSensor2 = analogRead(STATE_SENSOR2);
  
  // Atualizar string das leituras QTR
  sensorData.qtrReadings = "";
  for (uint8_t i = 0; i < SENSOR_COUNT; i++) {
    sensorData.qtrReadings += String(sensorData.qtrValues[i]);
    if (i < SENSOR_COUNT - 1) sensorData.qtrReadings += ",";
  }
}

uint16_t SensorManager::getLinePosition() {
  return sensorData.linePosition;
}

int SensorManager::getStateSensor1() {
  return sensorData.stateSensor1;
}

int SensorManager::getStateSensor2() {
  return sensorData.stateSensor2;
}

SensorData& SensorManager::getData() {
  return sensorData;
}

uint16_t* SensorManager::getQTRValues() {
  return sensorData.qtrValues;
}

String SensorManager::getQTRReadingsString() {
  return sensorData.qtrReadings;
}

void SensorManager::printCalibrationValues() {
  Serial.println("Valores de calibração:");
  Serial.print("Mínimos: ");
  for (uint8_t i = 0; i < SENSOR_COUNT; i++) {
    Serial.print(qtr.calibrationOn.minimum[i]);
    if (i < SENSOR_COUNT - 1) Serial.print(" ");
  }
  Serial.println();
  
  Serial.print("Máximos: ");
  for (uint8_t i = 0; i < SENSOR_COUNT; i++) {
    Serial.print(qtr.calibrationOn.maximum[i]);
    if (i < SENSOR_COUNT - 1) Serial.print(" ");
  }
  Serial.println();
}

void SensorManager::resetCalibration() {
  isCalibrated = false;
  Serial.println("Calibração resetada");
}
