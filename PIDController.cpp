#include "PIDController.h"

// ============================================================================
// IMPLEMENTAÇÃO DO CONTROLADOR PID DUAL
// ============================================================================

PIDController::PIDController() {
  // Inicializar variáveis PID
  P = I = D = 0;
  previousError = 0;
  pidValue = 0;
  error = 0;
  leftSpeed = 0;
  rightSpeed = 0;
  currentState = STATE_STRAIGHT;
}

void PIDController::begin() {
  Serial.println("=== Inicializando Controlador PID ===");
  
  // Configurar parâmetros padrão para reta
  straightParams.kp = PID_KP_STRAIGHT;
  straightParams.ki = PID_KI_STRAIGHT;
  straightParams.kd = PID_KD_STRAIGHT;
  straightParams.speed = SPEED_STRAIGHT;
  
  // Configurar parâmetros padrão para curva
  curveParams.kp = PID_KP_CURVE;
  curveParams.ki = PID_KI_CURVE;
  curveParams.kd = PID_KD_CURVE;
  curveParams.speed = SPEED_CURVE;
  
  reset();
  
  Serial.println("Parâmetros PID configurados:");
  printParameters();
  Serial.println("=====================================");
}

void PIDController::reset() {
  I = 0;
  previousError = 0;
  pidValue = 0;
  error = 0;
  leftSpeed = 0;
  rightSpeed = 0;
  
  Serial.println("PID resetado");
}

void PIDController::setStraightParams(float kp, float ki, float kd, int speed) {
  straightParams.kp = kp;
  straightParams.ki = ki;
  straightParams.kd = kd;
  straightParams.speed = speed;
  
  Serial.println("Parâmetros de reta atualizados:");
  Serial.println("Kp=" + String(kp) + " Ki=" + String(ki) + " Kd=" + String(kd) + " Vel=" + String(speed));
}

void PIDController::setCurveParams(float kp, float ki, float kd, int speed) {
  curveParams.kp = kp;
  curveParams.ki = ki;
  curveParams.kd = kd;
  curveParams.speed = speed;
  
  Serial.println("Parâmetros de curva atualizados:");
  Serial.println("Kp=" + String(kp) + " Ki=" + String(ki) + " Kd=" + String(kd) + " Vel=" + String(speed));
}

void PIDController::setCurrentState(RobotState state) {
  if (currentState != state) {
    currentState = state;
    reset(); // Reset PID ao mudar de estado
    Serial.println("Estado PID alterado para: " + String(state == STATE_STRAIGHT ? "RETA" : "CURVA"));
  }
}

void PIDController::calculate(int lineError) {
  error = lineError;
  
  // Selecionar parâmetros baseado no estado atual
  PIDParams params = getCurrentParams();
  
  // Cálculo PID
  P = error;
  I += error;
  D = error - previousError;
  
  // Limitar integral para evitar windup
  I = constrain(I, -PID_INTEGRAL_LIMIT, PID_INTEGRAL_LIMIT);
  
  // Calcular valor PID
  pidValue = (params.kp * P) + (params.ki * I) + (params.kd * D);
  previousError = error;
  
  // Calcular velocidades dos motores
  leftSpeed = params.speed - pidValue;
  rightSpeed = params.speed + pidValue;
  
  // Limitar velocidades
  leftSpeed = constrain(leftSpeed, -SPEED_LIMIT, SPEED_LIMIT);
  rightSpeed = constrain(rightSpeed, -SPEED_LIMIT, SPEED_LIMIT);
}

int PIDController::getLeftSpeed() {
  return leftSpeed;
}

int PIDController::getRightSpeed() {
  return rightSpeed;
}

int PIDController::getPIDValue() {
  return pidValue;
}

int PIDController::getError() {
  return error;
}

PIDParams PIDController::getStraightParams() {
  return straightParams;
}

PIDParams PIDController::getCurveParams() {
  return curveParams;
}

PIDParams PIDController::getCurrentParams() {
  return (currentState == STATE_STRAIGHT) ? straightParams : curveParams;
}

void PIDController::printPIDStatus() {
  Serial.println("Status PID:");
  Serial.println("Erro: " + String(error));
  Serial.println("P: " + String(P) + " I: " + String(I) + " D: " + String(D));
  Serial.println("PID: " + String(pidValue));
  Serial.println("Velocidades - E: " + String(leftSpeed) + " D: " + String(rightSpeed));
}

void PIDController::printParameters() {
  Serial.println("Reta - Kp:" + String(straightParams.kp) + " Ki:" + String(straightParams.ki) + 
                 " Kd:" + String(straightParams.kd) + " Vel:" + String(straightParams.speed));
  Serial.println("Curva - Kp:" + String(curveParams.kp) + " Ki:" + String(curveParams.ki) + 
                 " Kd:" + String(curveParams.kd) + " Vel:" + String(curveParams.speed));
}
