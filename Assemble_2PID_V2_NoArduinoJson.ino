// ============================================================================
// ROBÔ SEGUIDOR DE LINHA - RASPBERRY PI PICO W - VERSÃO MODULAR
// ============================================================================
// Board: earlephilhower/arduino-pico
// Modo: Cliente WiFi
// Bibliotecas: QTRSensors.h + WiFi.h
// ============================================================================

// Incluir bibliotecas principais
#include <QTRSensors.h>
#include <WiFi.h>

// Incluir módulos do sistema
#include "RobotConfig.h"
#include "SensorManager.h"
#include "MotorController.h"
#include "PIDController.h"
#include "StateMachine.h"
#include "WiFiManager.h"
#include "WebServer.h"
#include "PerformanceMonitor.h"

// Instâncias dos módulos
SensorManager sensorManager;
MotorController motorController;
PIDController pidController;
StateMachine stateMachine;
WiFiManager wifiManager;
WebServer webServer;
PerformanceMonitor perfMonitor;

// Variáveis globais para dados de status
StatusData statusData;
unsigned long startTime;

void setup() {
  Serial.begin(115200);
  delay(2000); // Aguardar estabilização do Serial

  Serial.println("=================================");
  Serial.println("ROBÔ SEGUIDOR DE LINHA - VERSÃO MODULAR");
  Serial.println("Raspberry Pi Pico W");
  Serial.println("Board: earlephilhower/arduino-pico");
  Serial.println("Modo: Cliente WiFi");
  Serial.println("=================================");

  // Inicializar timestamp
  startTime = millis();

  // Inicializar dados de status
  initializeStatusData();

  // Inicializar todos os módulos
  initializeModules();

  Serial.println("=================================");
  Serial.println("SISTEMA MODULAR INICIADO");
  Serial.println("Estado: Calibração");
  Serial.println("=================================");
}

void loop() {
  // Iniciar medição de performance
  perfMonitor.startLoop();

  // Processar comandos seriais (opcional - descomente se necessário)
  // processSerialCommands();

  // Monitor WiFi se ativo
  if (wifiManager.isActive() &&
      (statusData.currentState == STATE_CALIBRATION ||
       statusData.currentState == STATE_WAITING_COMMAND)) {
    wifiManager.monitor();
    webServer.handleRequests();
  }

  // Atualizar leituras dos sensores
  sensorManager.updateReadings();

  // Atualizar dados de status
  updateStatusData();

  // Executar máquina de estados
  executeStateMachine();

  // Finalizar medição de performance
  perfMonitor.endLoop();
}

// ============================================================================
// FUNÇÕES DE INICIALIZAÇÃO DO SISTEMA MODULAR
// ============================================================================

void initializeStatusData() {
  statusData.currentState = STATE_CALIBRATION;
  statusData.stateString = "CALIBRAÇÃO";
  statusData.error = 0;
  statusData.pidValue = 0;
  statusData.leftSpeed = 0;
  statusData.rightSpeed = 0;
  statusData.transitionCount = 0;
  statusData.stopTransitionCount = 0;
  statusData.uptime = 0;
  statusData.loopFrequency = 0;
  statusData.wifiActive = false;
  statusData.userConnected = false;
}

void initializeModules() {
  Serial.println("Inicializando módulos do sistema...");

  // Inicializar sensores
  sensorManager.begin();

  // Inicializar motores
  motorController.begin();

  // Inicializar controlador PID
  pidController.begin();

  // Inicializar máquina de estados
  stateMachine.begin();

  // Inicializar gerenciador WiFi
  wifiManager.begin();

  // Inicializar servidor web
  webServer.begin();
  webServer.setStatusData(&statusData);
  setupWebServerCallbacks();

  // Inicializar monitor de performance
  perfMonitor.begin();

  Serial.println("Todos os módulos inicializados com sucesso!");
}

// ============================================================================
// FUNÇÕES PRINCIPAIS DE EXECUÇÃO
// ============================================================================

void updateStatusData() {
  // Atualizar dados do estado
  statusData.currentState = stateMachine.getCurrentState();
  statusData.stateString = stateMachine.getStateString();
  statusData.transitionCount = stateMachine.getTransitionCount();
  statusData.stopTransitionCount = stateMachine.getStopTransitionCount();

  // Atualizar dados do PID
  statusData.error = pidController.getError();
  statusData.pidValue = pidController.getPIDValue();
  statusData.leftSpeed = pidController.getLeftSpeed();
  statusData.rightSpeed = pidController.getRightSpeed();

  // Atualizar dados de performance
  statusData.uptime = perfMonitor.getUptime();
  statusData.loopFrequency = perfMonitor.getLoopFrequency();

  // Atualizar dados de WiFi
  statusData.wifiActive = wifiManager.isActive();
}

void executeStateMachine() {
  RobotState currentState = stateMachine.getCurrentState();

  switch (currentState) {
    case STATE_CALIBRATION:
      handleCalibrationState();
      break;

    case STATE_WAITING_COMMAND:
      handleWaitingCommandState();
      break;

    case STATE_INITIAL:
      handleInitialState();
      break;

    case STATE_STRAIGHT:
    case STATE_CURVE:
      handleRunningState();
      break;

    case STATE_STOPPED:
      handleStoppedState();
      break;
  }
}

// ============================================================================
// FUNÇÕES DE TRATAMENTO DE ESTADOS
// ============================================================================

void handleCalibrationState() {
  if (stateMachine.isInCalibration()) {
    // Ativar WiFi durante calibração
    if (!wifiManager.isActive()) {
      if (wifiManager.connect()) {
        statusData.wifiActive = true;
        Serial.println("WiFi ativado para calibração");
      }
    }

    // Executar calibração dos sensores
    sensorManager.calibrate();
    stateMachine.finishCalibration();

    Serial.println("Calibração completa - Aguardando comando via browser");
  }
}

void handleWaitingCommandState() {
  // Piscar LED para indicar aguardando comando
  static unsigned long lastBlink = 0;
  static bool ledState = false;

  if (millis() - lastBlink >= 500) {
    ledState = !ledState;
    digitalWrite(LED_BUILTIN, ledState ? HIGH : LOW);
    lastBlink = millis();
  }

  // WiFi permanece ativo neste estado
}

void handleInitialState() {
  // Verificar sensor de estado para iniciar movimento
  int sensor1Value = sensorManager.getStateSensor1();

  if (sensor1Value < SENSOR_THRESHOLD) {
    stateMachine.setState(STATE_STRAIGHT);
    pidController.setCurrentState(STATE_STRAIGHT);
    pidController.reset();
    Serial.println("Iniciando movimento - Estado Reta");
  }
}

void handleRunningState() {
  // Calcular erro da linha
  uint16_t linePosition = sensorManager.getLinePosition();
  int error = 3500 - linePosition; // Centro da linha

  // Obter valores dos sensores de estado
  int sensor1 = sensorManager.getStateSensor1();
  int sensor2 = sensorManager.getStateSensor2();

  // Processar transições de estado
  stateMachine.processTransitions(sensor1, sensor2);

  // Atualizar estado do PID se necessário
  RobotState currentState = stateMachine.getCurrentState();
  pidController.setCurrentState(currentState);

  // Calcular PID e controlar motores
  pidController.calculate(error);
  motorController.setMotorSpeeds(pidController.getLeftSpeed(), pidController.getRightSpeed());
}

void handleStoppedState() {
  // Parar motores
  motorController.stopMotors();
  pidController.reset();

  // LED fixo para indicar parada
  digitalWrite(LED_BUILTIN, HIGH);
}

// ============================================================================
// CALLBACKS DO SERVIDOR WEB
// ============================================================================

void setupWebServerCallbacks() {
  // Configurar callbacks do servidor web
  webServer.onCalibrate = []() {
    Serial.println("Comando de calibração recebido via web");
    stateMachine.startCalibration();
    pidController.reset();
  };

  webServer.onStart = []() {
    Serial.println("Comando de início recebido via web");

    // Desativar WiFi antes de iniciar
    webServer.stop();
    wifiManager.disconnect();
    statusData.wifiActive = false;

    // Mudar para estado inicial
    stateMachine.setState(STATE_INITIAL);
    stateMachine.resetCounters();
    pidController.reset();

    Serial.println("WiFi desativado - Robô iniciado");
  };

  webServer.onStop = []() {
    Serial.println("Comando de parada recebido via web");
    stateMachine.setState(STATE_STOPPED);
    motorController.stopMotors();
    pidController.reset();
  };

  webServer.onParameterUpdate = [](float kp, float ki, float kd, int speed) {
    Serial.println("Atualizando parâmetros via web:");

    // Atualizar parâmetros do estado atual
    RobotState currentState = stateMachine.getCurrentState();
    if (currentState == STATE_STRAIGHT || currentState == STATE_CURVE) {
      if (currentState == STATE_STRAIGHT) {
        if (kp >= 0) pidController.setStraightParams(kp, ki >= 0 ? ki : PID_KI_STRAIGHT,
                                                    kd >= 0 ? kd : PID_KD_STRAIGHT,
                                                    speed >= 0 ? speed : SPEED_STRAIGHT);
      } else {
        if (kp >= 0) pidController.setCurveParams(kp, ki >= 0 ? ki : PID_KI_CURVE,
                                                 kd >= 0 ? kd : PID_KD_CURVE,
                                                 speed >= 0 ? speed : SPEED_CURVE);
      }
      Serial.println("Parâmetros atualizados com sucesso");
    }
  };
}

// ============================================================================
// FUNÇÕES UTILITÁRIAS E COMANDOS ESPECIAIS
// ============================================================================

void printSystemStatus() {
  Serial.println("=== STATUS DO SISTEMA MODULAR ===");

  // Status geral
  Serial.println("Estado: " + statusData.stateString);
  Serial.println("Uptime: " + perfMonitor.getFormattedUptime());
  Serial.println("Frequência: " + String(statusData.loopFrequency, 1) + " Hz");
  Serial.println("WiFi: " + String(statusData.wifiActive ? "Ativo" : "Inativo"));

  // Status dos sensores
  Serial.println("\n--- Sensores ---");
  Serial.println("Posição linha: " + String(sensorManager.getLinePosition()));
  Serial.println("Sensor 1: " + String(sensorManager.getStateSensor1()));
  Serial.println("Sensor 2: " + String(sensorManager.getStateSensor2()));

  // Status do PID
  Serial.println("\n--- PID ---");
  Serial.println("Erro: " + String(statusData.error));
  Serial.println("PID: " + String(statusData.pidValue));
  Serial.println("Velocidades - E:" + String(statusData.leftSpeed) + " D:" + String(statusData.rightSpeed));

  // Status das transições
  Serial.println("\n--- Transições ---");
  Serial.println("Reta/Curva: " + String(statusData.transitionCount));
  Serial.println("Parada: " + String(statusData.stopTransitionCount));

  Serial.println("=================================");
}

// Comando para teste dos módulos
void testAllModules() {
  Serial.println("=== TESTE DOS MÓDULOS ===");

  // Teste dos motores
  motorController.testMotors();

  // Teste dos sensores
  Serial.println("Testando sensores...");
  sensorManager.updateReadings();
  Serial.println("QTR: " + sensorManager.getQTRReadingsString());

  // Relatório de performance
  perfMonitor.printPerformanceReport();

  Serial.println("Teste concluído");
  Serial.println("=========================");
}

// ============================================================================
// COMANDOS SERIAIS PARA DEPURAÇÃO E CONTROLE
// ============================================================================

void processSerialCommands() {
  if (Serial.available()) {
    String command = Serial.readStringUntil('\n');
    command.trim();
    command.toLowerCase();

    if (command == "status") {
      printSystemStatus();
    }
    else if (command == "test") {
      testAllModules();
    }
    else if (command == "calibrate") {
      Serial.println("Iniciando calibração via serial...");
      stateMachine.startCalibration();
    }
    else if (command == "start") {
      if (stateMachine.canStartMovement()) {
        Serial.println("Iniciando movimento via serial...");
        stateMachine.setState(STATE_INITIAL);
      } else {
        Serial.println("Não é possível iniciar no estado atual");
      }
    }
    else if (command == "stop") {
      Serial.println("Parando robô via serial...");
      stateMachine.setState(STATE_STOPPED);
      motorController.stopMotors();
    }
    else if (command == "wifi") {
      if (wifiManager.isActive()) {
        wifiManager.printConnectionInfo();
      } else {
        Serial.println("WiFi inativo");
      }
    }
    else if (command == "performance") {
      perfMonitor.printPerformanceReport();
    }
    else if (command == "help") {
      printSerialHelp();
    }
    else if (command.length() > 0) {
      Serial.println("Comando desconhecido: " + command);
      Serial.println("Digite 'help' para ver comandos disponíveis");
    }
  }
}

void printSerialHelp() {
  Serial.println("=== COMANDOS SERIAIS DISPONÍVEIS ===");
  Serial.println("status      - Mostrar status do sistema");
  Serial.println("test        - Testar todos os módulos");
  Serial.println("calibrate   - Iniciar calibração");
  Serial.println("start       - Iniciar movimento");
  Serial.println("stop        - Parar robô");
  Serial.println("wifi        - Informações WiFi");
  Serial.println("performance - Relatório de performance");
  Serial.println("help        - Mostrar esta ajuda");
  Serial.println("====================================");
}

// ============================================================================
// INTEGRAÇÃO COM LOOP PRINCIPAL - PROCESSAMENTO SERIAL
// ============================================================================

// Adicionar processamento de comandos seriais ao loop principal
// Esta função deve ser chamada no loop() se desejado
void handleSerialInput() {
  processSerialCommands();
}

// ============================================================================
// SISTEMA MODULAR COMPLETO - VERSÃO FINAL
// ============================================================================
//
// Este arquivo agora utiliza uma arquitetura modular com os seguintes componentes:
//
// MÓDULOS PRINCIPAIS:
// - RobotConfig.h      : Configurações centralizadas
// - SensorManager      : Gerenciamento dos sensores QTR e de estado
// - MotorController    : Controle dos motores com compensação de zona morta
// - PIDController      : Controlador PID dual (reta/curva)
// - StateMachine       : Máquina de estados do robô
// - WiFiManager        : Gerenciamento da conexão WiFi
// - WebServer          : Servidor web para controle remoto
// - PerformanceMonitor : Monitoramento de performance do sistema
//
// VANTAGENS DA MODULARIZAÇÃO:
// 1. Código mais organizado e legível
// 2. Facilita manutenção e depuração
// 3. Permite reutilização de componentes
// 4. Melhor separação de responsabilidades
// 5. Facilita testes individuais de módulos
// 6. Escalabilidade para futuras funcionalidades
//
// COMANDOS SERIAIS DISPONÍVEIS:
// - status      : Mostrar status completo do sistema
// - test        : Testar todos os módulos
// - calibrate   : Iniciar calibração dos sensores
// - start       : Iniciar movimento do robô
// - stop        : Parar robô
// - wifi        : Informações da conexão WiFi
// - performance : Relatório de performance
// - help        : Mostrar ajuda dos comandos
//
// ============================================================================


