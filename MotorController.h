#ifndef MOTOR_CONTROLLER_H
#define MOTOR_CONTROLLER_H

#include "RobotConfig.h"

// ============================================================================
// CONTROLADOR DE MOTORES COM COMPENSAÇÃO DE ZONA MORTA
// ============================================================================

class MotorController {
private:
  int currentLeftSpeed;
  int currentRightSpeed;
  
  void controlMotor(uint8_t pinEN, uint8_t pinIN, int speed);

public:
  MotorController();
  
  // Inicialização
  void begin();
  
  // Controle de velocidade
  void setMotorSpeeds(int leftSpeed, int rightSpeed);
  void stopMotors();
  
  // Acesso aos dados
  int getLeftSpeed();
  int getRightSpeed();
  
  // Utilitários
  void testMotors();
  void printMotorStatus();
};

#endif // MOTOR_CONTROLLER_H
