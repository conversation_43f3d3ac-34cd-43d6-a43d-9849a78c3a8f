#include "PerformanceMonitor.h"

// ============================================================================
// IMPLEMENTAÇÃO DO MONITOR DE PERFORMANCE
// ============================================================================

PerformanceMonitor::PerformanceMonitor() {
  startTime = 0;
  loopCount = 0;
  loopFrequency = 0;
  lastFrequencyUpdate = 0;
  minLoopTime = ULONG_MAX;
  maxLoopTime = 0;
  totalLoopTime = 0;
  sampleCount = 0;
}

void PerformanceMonitor::begin() {
  Serial.println("=== Inicializando Monitor de Performance ===");
  
  startTime = millis();
  lastFrequencyUpdate = startTime;
  resetMetrics();
  
  Serial.println("Taxa de amostragem: " + String(PERFORMANCE_SAMPLE_RATE) + " loops");
  Serial.println("============================================");
}

void PerformanceMonitor::startLoop() {
  // Método pode ser usado para timing mais preciso se necessário
  // Por enquanto, usamos apenas endLoop() para simplicidade
}

void PerformanceMonitor::endLoop() {
  loopCount++;
  
  // Calcular frequência periodicamente
  if (loopCount % PERFORMANCE_SAMPLE_RATE == 0) {
    updateMetrics();
  }
}

void PerformanceMonitor::updateMetrics() {
  unsigned long currentTime = millis();
  unsigned long elapsed = currentTime - lastFrequencyUpdate;
  
  if (elapsed > 0) {
    // Calcular frequência baseada nos últimos PERFORMANCE_SAMPLE_RATE loops
    loopFrequency = (PERFORMANCE_SAMPLE_RATE * 1000.0) / elapsed;
    lastFrequencyUpdate = currentTime;
    
    // Atualizar métricas de timing (estimativa)
    float avgLoopTime = elapsed / (float)PERFORMANCE_SAMPLE_RATE;
    totalLoopTime += elapsed;
    sampleCount++;
    
    // Atualizar min/max (estimativa baseada na média)
    if (avgLoopTime < minLoopTime) {
      minLoopTime = (unsigned long)avgLoopTime;
    }
    if (avgLoopTime > maxLoopTime) {
      maxLoopTime = (unsigned long)avgLoopTime;
    }
  }
}

float PerformanceMonitor::getLoopFrequency() {
  return loopFrequency;
}

unsigned long PerformanceMonitor::getUptime() {
  return millis() - startTime;
}

unsigned long PerformanceMonitor::getLoopCount() {
  return loopCount;
}

unsigned long PerformanceMonitor::getMinLoopTime() {
  return minLoopTime == ULONG_MAX ? 0 : minLoopTime;
}

unsigned long PerformanceMonitor::getMaxLoopTime() {
  return maxLoopTime;
}

float PerformanceMonitor::getAverageLoopTime() {
  if (sampleCount == 0) return 0;
  return totalLoopTime / (float)(sampleCount * PERFORMANCE_SAMPLE_RATE);
}

void PerformanceMonitor::printPerformanceReport() {
  Serial.println("=== Relatório de Performance ===");
  Serial.println("Uptime: " + getFormattedUptime());
  Serial.println("Total de loops: " + String(loopCount));
  Serial.println("Frequência atual: " + String(loopFrequency, 2) + " Hz");
  Serial.println("Tempo médio por loop: " + String(getAverageLoopTime(), 2) + " ms");
  Serial.println("Tempo mínimo: " + String(getMinLoopTime()) + " ms");
  Serial.println("Tempo máximo: " + String(getMaxLoopTime()) + " ms");
  
  // Calcular loops por segundo médio
  unsigned long uptime = getUptime();
  if (uptime > 0) {
    float avgLoopsPerSecond = (loopCount * 1000.0) / uptime;
    Serial.println("Média geral: " + String(avgLoopsPerSecond, 2) + " loops/s");
  }
  
  Serial.println("================================");
}

void PerformanceMonitor::resetMetrics() {
  loopCount = 0;
  loopFrequency = 0;
  minLoopTime = ULONG_MAX;
  maxLoopTime = 0;
  totalLoopTime = 0;
  sampleCount = 0;
  startTime = millis();
  lastFrequencyUpdate = startTime;
  
  Serial.println("Métricas de performance resetadas");
}

bool PerformanceMonitor::shouldUpdateFrequency() {
  return (loopCount % PERFORMANCE_SAMPLE_RATE == 0);
}

String PerformanceMonitor::getFormattedUptime() {
  unsigned long uptime = getUptime();
  unsigned long seconds = uptime / 1000;
  unsigned long minutes = seconds / 60;
  unsigned long hours = minutes / 60;
  
  seconds %= 60;
  minutes %= 60;
  
  String formatted = "";
  if (hours > 0) {
    formatted += String(hours) + "h ";
  }
  if (minutes > 0) {
    formatted += String(minutes) + "m ";
  }
  formatted += String(seconds) + "s";
  
  return formatted;
}
