#ifndef PERFORMANCE_MONITOR_H
#define PERFORMANCE_MONITOR_H

#include "RobotConfig.h"

// ============================================================================
// MONITOR DE PERFORMANCE DO SISTEMA
// ============================================================================

class PerformanceMonitor {
private:
  unsigned long startTime;
  unsigned long loopCount;
  float loopFrequency;
  unsigned long lastFrequencyUpdate;
  
  // Métricas de timing
  unsigned long minLoopTime;
  unsigned long maxLoopTime;
  unsigned long totalLoopTime;
  
  // Controle de amostragem
  int sampleCount;

public:
  PerformanceMonitor();
  
  // Inicialização
  void begin();
  
  // Medição de performance
  void startLoop();
  void endLoop();
  void updateMetrics();
  
  // Acesso aos dados
  float getLoopFrequency();
  unsigned long getUptime();
  unsigned long getLoopCount();
  unsigned long getMinLoopTime();
  unsigned long getMaxLoopTime();
  float getAverageLoopTime();
  
  // Relatórios
  void printPerformanceReport();
  void resetMetrics();
  
  // Utilitários
  bool shouldUpdateFrequency();
  String getFormattedUptime();
};

#endif // PERFORMANCE_MONITOR_H
