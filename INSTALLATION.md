# Guia de Instalação - <PERSON><PERSON> Seguidor de Linha Modular

## Pré-requisitos

### Hardware Necessário
- Raspberry Pi Pico W
- 8x Sensores QTR (array de sensores de linha)
- 2x Sensores analógicos para detecção de estado
- 2x Motores DC com driver (ex: L298N)
- Fonte de alimentação adequada
- Chassis do robô
- Jumpers e protoboard

### Software Necessário
- Arduino IDE 2.0 ou superior
- Board package para Raspberry Pi Pico

## Instalação do Board Package

### 1. Configurar Arduino IDE

1. Abra o Arduino IDE
2. Vá em **File > Preferences**
3. No campo "Additional Boards Manager URLs", adicione:
   ```
   https://github.com/earlephilhower/arduino-pico/releases/download/global/package_rp2040_index.json
   ```
4. Clique em **OK**

### 2. Instalar Board Package

1. Vá em **Tools > Board > Boards Manager**
2. Procure por "pico"
3. Instale **"Raspberry Pi Pico/RP2040"** by <PERSON>, III
4. Aguarde a instalação completar

### 3. Configurar Board

1. Vá em **Tools > Board**
2. Selecione **"Raspberry Pi Pico W"**
3. Configure as opções:
   - **CPU Speed**: 133 MHz
   - **Flash Size**: 2MB (Sketch: 1MB, FS: 1MB)
   - **Debug Port**: Disabled
   - **Debug Level**: None
   - **USB Stack**: Pico SDK

## Instalação das Bibliotecas

### Biblioteca QTRSensors

#### Método 1: Library Manager (Recomendado)
1. Vá em **Tools > Manage Libraries**
2. Procure por "QTRSensors"
3. Instale **"QTRSensors"** by Pololu
4. Clique em **Install**

#### Método 2: Download Manual
1. Acesse: https://github.com/pololu/qtr-sensors-arduino
2. Clique em **Code > Download ZIP**
3. No Arduino IDE: **Sketch > Include Library > Add .ZIP Library**
4. Selecione o arquivo baixado

### Biblioteca WiFi
A biblioteca WiFi já está incluída no board package do Pico W, não é necessário instalar separadamente.

## Configuração do Projeto

### 1. Download do Código

1. Clone ou baixe este repositório
2. Extraia os arquivos em uma pasta
3. Abra o arquivo principal `Assemble_2PID_V2_NoArduinoJson.ino`

### 2. Configuração de Hardware

Edite o arquivo `RobotConfig.h` para ajustar às suas conexões:

```cpp
// Configurações dos Sensores QTR
#define SENSOR_COUNT 8
#define QTR_EMITTER_PIN 0
const uint8_t QTR_SENSOR_PINS[SENSOR_COUNT] = {1, 2, 3, 4, 5, 6, 7, 8};

// Configurações dos Motores
#define MOTOR_EN_A 15
#define MOTOR_IN1_A 14
#define MOTOR_IN1_B 16
#define MOTOR_EN_B 17

// Sensores de Estado
#define STATE_SENSOR1 26
#define STATE_SENSOR2 27
```

### 3. Configuração WiFi

No arquivo `RobotConfig.h`, configure suas credenciais WiFi:

```cpp
// Configurações WiFi
#define WIFI_SSID "Sua_Rede_WiFi"
#define WIFI_PASSWORD "Sua_Senha_WiFi"
```

### 4. Ajuste de Parâmetros PID

Configure os parâmetros PID conforme seu robô:

```cpp
// Configurações PID - Reta
#define PID_KP_STRAIGHT 0.06f
#define PID_KI_STRAIGHT 0.0f
#define PID_KD_STRAIGHT 0.45f
#define SPEED_STRAIGHT 100

// Configurações PID - Curva
#define PID_KP_CURVE 0.35f
#define PID_KI_CURVE 0.0f
#define PID_KD_CURVE 0.35f
#define SPEED_CURVE 100
```

## Compilação e Upload

### 1. Verificar Configurações

1. **Board**: Raspberry Pi Pico W
2. **Port**: Selecione a porta COM do Pico W
3. **Bibliotecas**: QTRSensors instalada

### 2. Compilar

1. Clique em **Verify** (✓) para compilar
2. Verifique se não há erros de compilação
3. Se houver erros, verifique as configurações e bibliotecas

### 3. Upload

1. Conecte o Pico W via USB
2. Pressione e segure o botão BOOTSEL no Pico W
3. Conecte o cabo USB (ainda segurando BOOTSEL)
4. Solte o botão BOOTSEL
5. Clique em **Upload** (→) no Arduino IDE
6. Aguarde o upload completar

## Teste Inicial

### 1. Monitor Serial

1. Abra **Tools > Serial Monitor**
2. Configure para **115200 baud**
3. Você deve ver mensagens de inicialização

### 2. Comandos de Teste

Digite no Serial Monitor:
- `help` - Lista de comandos
- `status` - Status do sistema
- `test` - Teste dos módulos
- `calibrate` - Iniciar calibração

### 3. Interface Web

1. Aguarde a conexão WiFi
2. Anote o IP mostrado no Serial Monitor
3. Acesse `http://IP_DO_PICO` no browser
4. Use a interface para controlar o robô

## Solução de Problemas

### Erro de Compilação
- Verifique se o board package está instalado
- Confirme que a biblioteca QTRSensors está instalada
- Verifique se todos os arquivos .h e .cpp estão na mesma pasta

### Erro de Upload
- Verifique se o Pico W está em modo BOOTSEL
- Tente uma porta USB diferente
- Reinicie o Arduino IDE

### WiFi não Conecta
- Verifique SSID e senha no RobotConfig.h
- Confirme que a rede é 2.4GHz (Pico W não suporta 5GHz)
- Verifique a força do sinal WiFi

### Sensores não Funcionam
- Verifique as conexões dos pinos
- Confirme a alimentação dos sensores
- Teste com o comando `test` no Serial Monitor

## Estrutura de Arquivos Final

```
Assemble_2PID_V2_Augment/
├── Assemble_2PID_V2_NoArduinoJson.ino
├── RobotConfig.h
├── SensorManager.h
├── SensorManager.cpp
├── MotorController.h
├── MotorController.cpp
├── PIDController.h
├── PIDController.cpp
├── StateMachine.h
├── StateMachine.cpp
├── WiFiManager.h
├── WiFiManager.cpp
├── WebServer.h
├── WebServer.cpp
├── PerformanceMonitor.h
├── PerformanceMonitor.cpp
├── README.md
├── INSTALLATION.md
└── examples/
    └── SimpleLineFollower.ino
```

## Próximos Passos

Após a instalação bem-sucedida:

1. **Calibração**: Execute a calibração dos sensores
2. **Teste**: Use os comandos seriais para testar cada módulo
3. **Ajuste**: Configure os parâmetros PID conforme necessário
4. **Operação**: Use a interface web ou comandos seriais para operar o robô

Para suporte adicional, consulte o README.md ou abra uma issue no repositório.
