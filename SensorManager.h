#ifndef SENSOR_MANAGER_H
#define SENSOR_MANAGER_H

#include <QTRSensors.h>
#include "RobotConfig.h"

// ============================================================================
// GERENCIADOR DE SENSORES - QTR E SENSORES DE ESTADO
// ============================================================================

class SensorManager {
private:
  QTRSensors qtr;
  SensorData sensorData;
  bool isCalibrated;

public:
  SensorManager();
  
  // Inicialização
  void begin();
  void calibrate();
  bool isCalibrationComplete();
  
  // Leitura dos sensores
  void updateReadings();
  uint16_t getLinePosition();
  int getStateSensor1();
  int getStateSensor2();
  
  // Acesso aos dados
  SensorData& getData();
  uint16_t* getQTRValues();
  String getQTRReadingsString();
  
  // Utilitários
  void printCalibrationValues();
  void resetCalibration();
};

#endif // SENSOR_MANAGER_H
