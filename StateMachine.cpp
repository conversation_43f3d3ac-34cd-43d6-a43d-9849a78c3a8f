#include "StateMachine.h"

// ============================================================================
// IMPLEMENTAÇÃO DA MÁQUINA DE ESTADOS
// ============================================================================

StateMachine::StateMachine() {
  currentState = STATE_CALIBRATION;
  stateString = "CALIBRAÇÃO";
  condition1Previous = false;
  condition2Previous = false;
  condition2SensorPrevious = false;
  transitionCount = 0;
  stopTransitionCount = 0;
  isCalibrating = true;
}

void StateMachine::begin() {
  Serial.println("=== Inicializando Máquina de Estados ===");
  
  currentState = STATE_CALIBRATION;
  updateStateString();
  resetCounters();
  
  Serial.println("Estado inicial: " + stateString);
  Serial.println("Transições necessárias para mudança: " + String(TRANSITIONS_FOR_STATE_CHANGE));
  Serial.println("========================================");
}

void StateMachine::setState(RobotState newState) {
  if (currentState != newState) {
    RobotState previousState = currentState;
    currentState = newState;
    updateStateString();
    
    Serial.println("Mudança de estado: " + String(previousState) + " -> " + stateString);
    
    // Reset contadores ao mudar estado
    if (newState == STATE_STRAIGHT || newState == STATE_CURVE) {
      resetCounters();
    }
  }
}

RobotState StateMachine::getCurrentState() {
  return currentState;
}

String StateMachine::getStateString() {
  return stateString;
}

void StateMachine::updateStateString() {
  switch (currentState) {
    case STATE_CALIBRATION:
      stateString = "CALIBRAÇÃO";
      break;
    case STATE_WAITING_COMMAND:
      stateString = "AGUARDANDO COMANDO";
      break;
    case STATE_INITIAL:
      stateString = "INICIAL";
      break;
    case STATE_STRAIGHT:
      stateString = "RETA";
      break;
    case STATE_CURVE:
      stateString = "CURVA";
      break;
    case STATE_STOPPED:
      stateString = "PARADO";
      break;
  }
}

void StateMachine::processTransitions(int sensor1, int sensor2) {
  if (currentState != STATE_INITIAL && currentState != STATE_STOPPED) {
    detectTransitions(sensor1, sensor2);
    
    // Verificar se deve parar (prioridade sobre mudança de estado)
    if (stopTransitionCount >= TRANSITIONS_FOR_STATE_CHANGE) {
      setState(STATE_STOPPED);
      resetCounters();
      Serial.println("Robô parado por detecção de transição");
    }
    // Verificar se deve mudar estado (reta/curva)
    else if (transitionCount >= TRANSITIONS_FOR_STATE_CHANGE) {
      changeState();
      transitionCount = 0;
    }
  }
}

void StateMachine::detectTransitions(int sensor1, int sensor2) {
  bool condition1 = (sensor1 < SENSOR_THRESHOLD);
  bool condition2 = (sensor1 > SENSOR_THRESHOLD);
  bool condition2Sensor = (sensor2 < SENSOR_THRESHOLD);
  
  // Detectar transições para mudança de estado (reta/curva) usando Sensor1
  if (condition1Previous && !condition1 && condition2) {
    transitionCount++;
    Serial.println("Transição reta/curva detectada: " + String(transitionCount));
  }
  if (condition2Previous && !condition2 && condition1) {
    transitionCount++;
    Serial.println("Transição reta/curva detectada: " + String(transitionCount));
  }
  
  // Detectar transições para parada usando Sensor2
  if (!condition2SensorPrevious && condition2Sensor) {
    stopTransitionCount++;
    Serial.println("Transição parada detectada: " + String(stopTransitionCount));
  }
  if (condition2SensorPrevious && !condition2Sensor) {
    stopTransitionCount++;
    Serial.println("Transição parada detectada: " + String(stopTransitionCount));
  }
  
  // Atualizar estados anteriores
  condition1Previous = condition1;
  condition2Previous = condition2;
  condition2SensorPrevious = condition2Sensor;
}

void StateMachine::changeState() {
  if (currentState == STATE_STRAIGHT) {
    setState(STATE_CURVE);
  } else if (currentState == STATE_CURVE) {
    setState(STATE_STRAIGHT);
  }
}

void StateMachine::startCalibration() {
  setState(STATE_CALIBRATION);
  isCalibrating = true;
  resetCounters();
  Serial.println("Calibração iniciada");
}

void StateMachine::finishCalibration() {
  isCalibrating = false;
  setState(STATE_WAITING_COMMAND);
  Serial.println("Calibração finalizada - Aguardando comando");
}

bool StateMachine::isInCalibration() {
  return isCalibrating;
}

int StateMachine::getTransitionCount() {
  return transitionCount;
}

int StateMachine::getStopTransitionCount() {
  return stopTransitionCount;
}

void StateMachine::resetCounters() {
  transitionCount = 0;
  stopTransitionCount = 0;
  Serial.println("Contadores de transição resetados");
}

void StateMachine::printStateInfo() {
  Serial.println("=== Estado Atual ===");
  Serial.println("Estado: " + stateString);
  Serial.println("Transições: " + String(transitionCount));
  Serial.println("Transições Parada: " + String(stopTransitionCount));
  Serial.println("Calibrando: " + String(isCalibrating ? "Sim" : "Não"));
  Serial.println("===================");
}

bool StateMachine::canStartMovement() {
  return (currentState == STATE_WAITING_COMMAND || currentState == STATE_STOPPED);
}

bool StateMachine::shouldStopMovement() {
  return (currentState == STATE_STOPPED);
}
