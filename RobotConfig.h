#ifndef ROBOT_CONFIG_H
#define ROBOT_CONFIG_H

// ============================================================================
// CONFIGURAÇÕES CENTRALIZADAS DO ROBÔ SEGUIDOR DE LINHA
// ============================================================================

// Incluir tipos básicos do Arduino
#include <Arduino.h>

// Configurações dos Sensores QTR
#define SENSOR_COUNT 8
#define QTR_EMITTER_PIN 0
const uint8_t QTR_SENSOR_PINS[SENSOR_COUNT] = {1, 2, 3, 4, 5, 6, 7, 8};

// Configurações dos Motores
#define MOTOR_EN_A 15
#define MOTOR_IN1_A 14
#define MOTOR_IN1_B 16
#define MOTOR_EN_B 17
#define MOTOR_DEADBAND 20  // Compensação de zona morta

// Sensores de Estado
#define STATE_SENSOR1 26
#define STATE_SENSOR2 27
#define SENSOR_THRESHOLD 500

// Configurações PID - Reta
#define PID_KP_STRAIGHT 0.06f
#define PID_KI_STRAIGHT 0.0f
#define PID_KD_STRAIGHT 0.45f
#define SPEED_STRAIGHT 100

// Configurações PID - Curva
#define PID_KP_CURVE 0.35f
#define PID_KI_CURVE 0.0f
#define PID_KD_CURVE 0.35f
#define SPEED_CURVE 100

// Configurações da Máquina de Estados
#define TRANSITIONS_FOR_STATE_CHANGE 2
#define PID_INTEGRAL_LIMIT 10000
#define SPEED_LIMIT 250

// Configurações WiFi
#define WIFI_SSID "Galaxy A72 3FB2"
#define WIFI_PASSWORD "bawpa0052"
#define WIFI_MAX_RETRIES 3
#define WIFI_TIMEOUT_ATTEMPTS 40
#define WIFI_CHECK_INTERVAL 5000
#define WIFI_MAX_RECONNECT_ATTEMPTS 3

// Configurações do Servidor Web
#define WEB_SERVER_PORT 80
#define HTTP_TIMEOUT 1000

// Configurações de Performance
#define PERFORMANCE_SAMPLE_RATE 100  // Calcular frequência a cada N loops

// Estados do Sistema
enum RobotState {
  STATE_CALIBRATION,
  STATE_WAITING_COMMAND,
  STATE_INITIAL,
  STATE_STRAIGHT,
  STATE_CURVE,
  STATE_STOPPED
};

// Estrutura para parâmetros PID
struct PIDParams {
  float kp, ki, kd;
  int speed;
};

// Estrutura para dados dos sensores
struct SensorData {
  uint16_t qtrValues[SENSOR_COUNT];
  uint16_t linePosition;
  int stateSensor1;
  int stateSensor2;
  String qtrReadings;
};

// Estrutura para dados de status
struct StatusData {
  RobotState currentState;
  String stateString;
  int error;
  int pidValue;
  int leftSpeed;
  int rightSpeed;
  int transitionCount;
  int stopTransitionCount;
  unsigned long uptime;
  float loopFrequency;
  bool wifiActive;
  bool userConnected;
};

#endif // ROBOT_CONFIG_H
