#include "WiFiManager.h"

// ============================================================================
// IMPLEMENTAÇÃO DO GERENCIADOR WiFi
// ============================================================================

WiFiManager::WiFiManager() {
  wifiActive = false;
  userConnected = false;
  lastWiFiCheck = 0;
  reconnectAttempts = 0;
}

void WiFiManager::begin() {
  Serial.println("=== Inicializando Gerenciador WiFi ===");
  Serial.println("Modo: Cliente WiFi (Raspberry Pi Pico W)");
  Serial.println("Board Package: earlephilhower/arduino-pico");
  Serial.println("SSID: " + String(WIFI_SSID));
  Serial.println("======================================");
}

bool WiFiManager::connect() {
  Serial.println("=== Iniciando conexão WiFi ===");
  
  // Validar credenciais
  if (strlen(WIFI_SSID) == 0 || strlen(WIFI_PASSWORD) == 0) {
    Serial.println("ERRO: Credenciais WiFi vazias!");
    return false;
  }
  
  // Desconectar conexão anterior
  WiFi.disconnect(true);
  delay(1000);
  
  // Configurar modo cliente
  WiFi.mode(WIFI_STA);
  WiFi.setSleep(false); // Desabilitar sleep para melhor estabilidade
  
  Serial.print("Conectando a: ");
  Serial.println(WIFI_SSID);
  
  // Múltiplas tentativas de conexão
  bool connected = false;
  for (int retry = 0; retry < WIFI_MAX_RETRIES && !connected; retry++) {
    if (retry > 0) {
      Serial.println("Tentativa " + String(retry + 1) + " de " + String(WIFI_MAX_RETRIES));
      WiFi.disconnect(true);
      delay(2000);
      WiFi.mode(WIFI_STA);
      delay(1000);
    }
    
    connected = attemptConnection();
  }
  
  if (connected) {
    wifiActive = true;
    reconnectAttempts = 0;
    printConnectionInfo();
    return true;
  } else {
    Serial.println("ERRO: Falha ao conectar ao WiFi");
    wifiActive = false;
    return false;
  }
}

bool WiFiManager::attemptConnection() {
  WiFi.begin(WIFI_SSID, WIFI_PASSWORD);
  
  int attempts = 0;
  while (WiFi.status() != WL_CONNECTED && attempts < WIFI_TIMEOUT_ATTEMPTS) {
    delay(500);
    Serial.print(".");
    attempts++;
    
    // Verificar condições de erro
    wl_status_t status = WiFi.status();
    if (status == WL_CONNECT_FAILED) {
      Serial.println("\nERRO: Falha na conexão (senha incorreta?)");
      return false;
    } else if (status == WL_NO_SSID_AVAIL) {
      Serial.println("\nERRO: SSID não encontrado");
      return false;
    }
    
    // Status periódico
    if (attempts % 10 == 0) {
      Serial.print("\nStatus: ");
      printWiFiStatus(status);
      Serial.print("Tentativa " + String(attempts) + "/" + String(WIFI_TIMEOUT_ATTEMPTS) + " ");
    }
  }
  
  return (WiFi.status() == WL_CONNECTED);
}

void WiFiManager::disconnect() {
  if (wifiActive) {
    Serial.println("=== Desativando WiFi ===");
    
    WiFi.disconnect(true);
    delay(500);
    WiFi.mode(WIFI_OFF);
    delay(100);
    
    wifiActive = false;
    userConnected = false;
    
    Serial.println("WiFi desativado - modo offline");
    Serial.println("========================");
  }
}

void WiFiManager::monitor() {
  if (!wifiActive) return;
  
  unsigned long now = millis();
  if (now - lastWiFiCheck >= WIFI_CHECK_INTERVAL) {
    lastWiFiCheck = now;
    
    if (WiFi.status() != WL_CONNECTED) {
      Serial.println("⚠️ WiFi desconectado!");
      printWiFiStatus(WiFi.status());
      
      if (reconnectAttempts < WIFI_MAX_RECONNECT_ATTEMPTS) {
        reconnectAttempts++;
        Serial.println("Tentando reconectar (" + String(reconnectAttempts) + "/" + 
                      String(WIFI_MAX_RECONNECT_ATTEMPTS) + ")...");
        
        WiFi.disconnect();
        delay(1000);
        WiFi.begin(WIFI_SSID, WIFI_PASSWORD);
        
        // Aguardar reconexão (timeout menor)
        int attempts = 0;
        while (WiFi.status() != WL_CONNECTED && attempts < 20) {
          delay(500);
          Serial.print(".");
          attempts++;
        }
        
        if (WiFi.status() == WL_CONNECTED) {
          Serial.println("\n✅ WiFi reconectado!");
          Serial.println("IP: " + WiFi.localIP().toString());
          reconnectAttempts = 0;
        } else {
          Serial.println("\n❌ Falha na reconexão");
        }
      } else {
        Serial.println("❌ Máximo de tentativas atingido - desativando WiFi");
        disconnect();
      }
    } else {
      // Reset contador se conexão estável
      if (reconnectAttempts > 0) {
        reconnectAttempts = 0;
      }
    }
  }
}

bool WiFiManager::isActive() {
  return wifiActive;
}

bool WiFiManager::isConnected() {
  return wifiActive && (WiFi.status() == WL_CONNECTED);
}

bool WiFiManager::hasUserConnected() {
  return userConnected;
}

void WiFiManager::setUserConnected(bool connected) {
  userConnected = connected;
}

String WiFiManager::getSSID() {
  return WiFi.SSID();
}

String WiFiManager::getLocalIP() {
  return WiFi.localIP().toString();
}

String WiFiManager::getGatewayIP() {
  return WiFi.gatewayIP().toString();
}

String WiFiManager::getDNSIP() {
  return WiFi.dnsIP().toString();
}

String WiFiManager::getMACAddress() {
  return WiFi.macAddress();
}

int WiFiManager::getRSSI() {
  return WiFi.RSSI();
}

void WiFiManager::printWiFiStatus(wl_status_t status) {
  switch (status) {
    case WL_IDLE_STATUS: Serial.println("WL_IDLE_STATUS"); break;
    case WL_NO_SSID_AVAIL: Serial.println("WL_NO_SSID_AVAIL"); break;
    case WL_SCAN_COMPLETED: Serial.println("WL_SCAN_COMPLETED"); break;
    case WL_CONNECTED: Serial.println("WL_CONNECTED"); break;
    case WL_CONNECT_FAILED: Serial.println("WL_CONNECT_FAILED"); break;
    case WL_CONNECTION_LOST: Serial.println("WL_CONNECTION_LOST"); break;
    case WL_DISCONNECTED: Serial.println("WL_DISCONNECTED"); break;
    default: Serial.println("Status: " + String(status)); break;
  }
}

void WiFiManager::printConnectionInfo() {
  Serial.println("=== WiFi Conectado ===");
  Serial.println("SSID: " + getSSID());
  Serial.println("IP: " + getLocalIP());
  Serial.println("Gateway: " + getGatewayIP());
  Serial.println("DNS: " + getDNSIP());
  Serial.println("RSSI: " + String(getRSSI()) + " dBm");
  Serial.println("MAC: " + getMACAddress());
  Serial.println("======================");
}
