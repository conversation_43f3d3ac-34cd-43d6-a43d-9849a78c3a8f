#include "MotorController.h"

// ============================================================================
// IMPLEMENTAÇÃO DO CONTROLADOR DE MOTORES
// ============================================================================

MotorController::MotorController() : currentLeftSpeed(0), currentRightSpeed(0) {
}

void MotorController::begin() {
  Serial.println("=== Inicializando Motores ===");
  
  // Configurar pinos dos motores como saída
  pinMode(MOTOR_IN1_A, OUTPUT);
  pinMode(MOTOR_IN1_B, OUTPUT);
  pinMode(MOTOR_EN_A, OUTPUT);
  pinMode(MOTOR_EN_B, OUTPUT);
  pinMode(LED_BUILTIN, OUTPUT);
  
  // Inicializar motores parados
  analogWrite(MOTOR_IN1_A, 0);
  analogWrite(MOTOR_IN1_B, 0);
  analogWrite(MOTOR_EN_A, 0);
  analogWrite(MOTOR_EN_B, 0);
  
  Serial.println("Pinos dos motores configurados:");
  Serial.println("Motor A - EN: " + String(MOTOR_EN_A) + ", IN1: " + String(MOTOR_IN1_A));
  Serial.println("Motor B - EN: " + String(MOTOR_EN_B) + ", IN1: " + String(MOTOR_IN1_B));
  Serial.println("Zona morta: " + String(MOTOR_DEADBAND));
  Serial.println("=============================");
}

void MotorController::setMotorSpeeds(int leftSpeed, int rightSpeed) {
  // Limitar velocidades
  leftSpeed = constrain(leftSpeed, -SPEED_LIMIT, SPEED_LIMIT);
  rightSpeed = constrain(rightSpeed, -SPEED_LIMIT, SPEED_LIMIT);
  
  // Armazenar velocidades atuais
  currentLeftSpeed = leftSpeed;
  currentRightSpeed = rightSpeed;
  
  // Controlar motores
  controlMotor(MOTOR_EN_A, MOTOR_IN1_A, rightSpeed);  // Motor direito
  controlMotor(MOTOR_EN_B, MOTOR_IN1_B, leftSpeed);   // Motor esquerdo
}

void MotorController::controlMotor(uint8_t pinEN, uint8_t pinIN, int speed) {
  // Aplicar compensação de zona morta
  if (speed > 0) {
    // Movimento para frente
    if (speed < MOTOR_DEADBAND) speed = 0;
    analogWrite(pinEN, speed);
    digitalWrite(pinIN, LOW);
  } else if (speed < 0) {
    // Movimento para trás
    if (speed > -MOTOR_DEADBAND) speed = 0;
    analogWrite(pinIN, -speed);
    digitalWrite(pinEN, LOW);
  } else {
    // Parada completa
    digitalWrite(pinEN, LOW);
    digitalWrite(pinIN, LOW);
  }
}

void MotorController::stopMotors() {
  analogWrite(MOTOR_EN_A, 0);
  analogWrite(MOTOR_EN_B, 0);
  digitalWrite(MOTOR_IN1_A, LOW);
  digitalWrite(MOTOR_IN1_B, LOW);
  
  currentLeftSpeed = 0;
  currentRightSpeed = 0;
  
  Serial.println("Motores parados");
}

int MotorController::getLeftSpeed() {
  return currentLeftSpeed;
}

int MotorController::getRightSpeed() {
  return currentRightSpeed;
}

void MotorController::testMotors() {
  Serial.println("=== Teste dos Motores ===");
  
  Serial.println("Testando motor esquerdo...");
  setMotorSpeeds(100, 0);
  delay(1000);
  stopMotors();
  delay(500);
  
  Serial.println("Testando motor direito...");
  setMotorSpeeds(0, 100);
  delay(1000);
  stopMotors();
  delay(500);
  
  Serial.println("Testando ambos os motores...");
  setMotorSpeeds(100, 100);
  delay(1000);
  stopMotors();
  
  Serial.println("Teste concluído");
  Serial.println("=========================");
}

void MotorController::printMotorStatus() {
  Serial.println("Status dos Motores:");
  Serial.println("Esquerdo: " + String(currentLeftSpeed));
  Serial.println("Direito: " + String(currentRightSpeed));
}
