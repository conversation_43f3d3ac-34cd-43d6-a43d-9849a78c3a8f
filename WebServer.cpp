#include "WebServer.h"

// ============================================================================
// IMPLEMENTAÇÃO DO SERVIDOR WEB
// ============================================================================

WebServer::WebServer() : server(WEB_SERVER_PORT) {
  statusData = nullptr;
}

void WebServer::begin() {
  Serial.println("=== Iniciando Servidor Web ===");
  server.begin();
  Serial.println("Servidor HTTP ativo na porta " + String(WEB_SERVER_PORT));
  Serial.println("==============================");
}

void WebServer::stop() {
  Serial.println("Parando servidor web...");
  server.stop();
}

void WebServer::setStatusData(StatusData* data) {
  statusData = data;
}

void WebServer::handleRequests() {
  WiFiClient client = server.accept();
  if (client) {
    // Marcar que usuário se conectou
    if (statusData) {
      statusData->userConnected = true;
    }
    
    String request = "";
    unsigned long timeout = millis() + HTTP_TIMEOUT;
    
    // Ler requisição HTTP com timeout
    while (client.connected() && millis() < timeout) {
      if (client.available()) {
        String line = client.readStringUntil('\r');
        if (line.length() == 1) break; // Fim dos headers
        if (request.length() == 0) request = line; // Primeira linha
      }
      delay(1);
    }
    
    // Processar requisição
    bool handled = false;
    
    if (request.indexOf("GET / ") >= 0) {
      sendHTMLPage(client);
      handled = true;
    }
    else if (request.indexOf("GET /status") >= 0) {
      sendStatusResponse(client);
      handled = true;
    }
    else if (request.indexOf("GET /update") >= 0) {
      if (processParameterUpdate(request)) {
        sendSimpleResponse(client, "Parâmetros atualizados");
      } else {
        sendSimpleResponse(client, "Erro ao atualizar parâmetros");
      }
      handled = true;
    }
    else if (request.indexOf("GET /calibrate") >= 0) {
      if (processCalibrationCommand()) {
        sendSimpleResponse(client, "Calibração iniciada");
      } else {
        sendSimpleResponse(client, "Erro ao iniciar calibração");
      }
      handled = true;
    }
    else if (request.indexOf("GET /start") >= 0) {
      if (processStartCommand()) {
        sendSimpleResponse(client, "Robô iniciado - WiFi será desativado");
        client.stop(); // Fechar conexão antes de desativar WiFi
        delay(500); // Garantir que resposta seja enviada
        return; // Não continuar processamento
      } else {
        sendSimpleResponse(client, "Robô já em execução");
      }
      handled = true;
    }
    else if (request.indexOf("GET /stop") >= 0) {
      if (processStopCommand()) {
        sendSimpleResponse(client, "Robô parado");
      } else {
        sendSimpleResponse(client, "Erro ao parar robô");
      }
      handled = true;
    }
    
    if (!handled) {
      send404Response(client);
    }
    
    client.stop();
  }
}

bool WebServer::processParameterUpdate(String request) {
  if (!onParameterUpdate) return false;
  
  float kp = -1, ki = -1, kd = -1;
  int speed = -1;
  
  // Parsing simples de parâmetros URL
  if (request.indexOf("kp=") >= 0) {
    int start = request.indexOf("kp=") + 3;
    int end = request.indexOf("&", start);
    if (end == -1) end = request.indexOf(" ", start);
    kp = request.substring(start, end).toFloat();
  }
  
  if (request.indexOf("ki=") >= 0) {
    int start = request.indexOf("ki=") + 3;
    int end = request.indexOf("&", start);
    if (end == -1) end = request.indexOf(" ", start);
    ki = request.substring(start, end).toFloat();
  }
  
  if (request.indexOf("kd=") >= 0) {
    int start = request.indexOf("kd=") + 3;
    int end = request.indexOf("&", start);
    if (end == -1) end = request.indexOf(" ", start);
    kd = request.substring(start, end).toFloat();
  }
  
  if (request.indexOf("speed=") >= 0) {
    int start = request.indexOf("speed=") + 6;
    int end = request.indexOf("&", start);
    if (end == -1) end = request.indexOf(" ", start);
    speed = request.substring(start, end).toInt();
  }
  
  if (kp >= 0 || ki >= 0 || kd >= 0 || speed >= 0) {
    onParameterUpdate(kp, ki, kd, speed);
    return true;
  }
  
  return false;
}

bool WebServer::processCalibrationCommand() {
  if (onCalibrate) {
    onCalibrate();
    return true;
  }
  return false;
}

bool WebServer::processStartCommand() {
  if (onStart && statusData) {
    if (statusData->currentState == STATE_WAITING_COMMAND || 
        statusData->currentState == STATE_STOPPED) {
      onStart();
      return true;
    }
  }
  return false;
}

bool WebServer::processStopCommand() {
  if (onStop) {
    onStop();
    return true;
  }
  return false;
}

bool WebServer::isRunning() {
  return true; // WiFiServer não tem método para verificar se está rodando
}

void WebServer::sendHTMLPage(WiFiClient &client) {
  // Headers HTTP
  client.println("HTTP/1.1 200 OK");
  client.println("Content-Type: text/html");
  client.println("Connection: close");
  client.println();
  
  // HTML básico (será expandido se necessário)
  client.println("<!DOCTYPE html><html><head>");
  client.println("<title>Robot Control - Pico W</title>");
  client.println("<meta name='viewport' content='width=device-width, initial-scale=1'>");
  client.println("<meta http-equiv='refresh' content='3'>");
  client.println("</head><body>");
  client.println("<h1>🤖 Robô Seguidor de Linha</h1>");
  
  if (statusData) {
    client.println("<h2>Status</h2>");
    client.println("<p>Estado: " + statusData->stateString + "</p>");
    client.println("<p>Erro: " + String(statusData->error) + "</p>");
    client.println("<p>PID: " + String(statusData->pidValue) + "</p>");
    client.println("<p>Velocidades - E:" + String(statusData->leftSpeed) + 
                   " D:" + String(statusData->rightSpeed) + "</p>");
    client.println("<p>Uptime: " + String(statusData->uptime/1000) + "s</p>");
    
    client.println("<h2>Controles</h2>");
    client.println("<p><a href='/calibrate'>🔧 Calibrar</a></p>");
    client.println("<p><a href='/start'>▶️ Iniciar</a></p>");
    client.println("<p><a href='/stop'>⏹️ Parar</a></p>");
    client.println("<p><a href='/status'>📊 Status JSON</a></p>");
  }
  
  client.println("</body></html>");
}

void WebServer::sendStatusResponse(WiFiClient &client) {
  client.println("HTTP/1.1 200 OK");
  client.println("Content-Type: text/plain");
  client.println("Connection: close");
  client.println();
  
  if (statusData) {
    String response = "Estado:" + statusData->stateString;
    response += "|Erro:" + String(statusData->error);
    response += "|PID:" + String(statusData->pidValue);
    response += "|VelE:" + String(statusData->leftSpeed);
    response += "|VelD:" + String(statusData->rightSpeed);
    response += "|Trans:" + String(statusData->transitionCount);
    response += "|Tempo:" + String(statusData->uptime/1000);
    client.println(response);
  } else {
    client.println("Dados não disponíveis");
  }
}

void WebServer::sendSimpleResponse(WiFiClient &client, String message) {
  client.println("HTTP/1.1 200 OK");
  client.println("Content-Type: text/plain");
  client.println("Connection: close");
  client.println();
  client.println(message);
}

void WebServer::send404Response(WiFiClient &client) {
  client.println("HTTP/1.1 404 Not Found");
  client.println("Content-Type: text/plain");
  client.println("Connection: close");
  client.println();
  client.println("404 - Página não encontrada");
}
