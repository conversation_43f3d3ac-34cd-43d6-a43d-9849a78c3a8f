// ============================================================================
// EXEMPLO SIMPLES - ROBÔ SEGUIDOR DE LINHA MODULAR
// ============================================================================
// Este exemplo mostra como usar o sistema modular de forma simplificada
// sem WiFi, focando apenas no seguimento de linha básico.

#include <QTRSensors.h>
#include "../RobotConfig.h"
#include "../SensorManager.h"
#include "../MotorController.h"
#include "../PIDController.h"
#include "../StateMachine.h"
#include "../PerformanceMonitor.h"

// Instâncias dos módulos necessários
SensorManager sensorManager;
MotorController motorController;
PIDController pidController;
StateMachine stateMachine;
PerformanceMonitor perfMonitor;

// Dados de status simplificados
StatusData statusData;

void setup() {
  Serial.begin(115200);
  delay(2000);

  Serial.println("=== EXEMPLO SIMPLES - SEGUIDOR DE LINHA ===");
  Serial.println("Versão sem WiFi - Apenas seguimento básico");
  Serial.println("==========================================");

  // Inicializar dados de status
  initializeStatusData();

  // Inicializar módulos essenciais
  sensorManager.begin();
  motorController.begin();
  pidController.begin();
  stateMachine.begin();
  perfMonitor.begin();

  // Configurar parâmetros PID mais conservadores para exemplo
  pidController.setStraightParams(0.05, 0.0, 0.3, 80);
  pidController.setCurveParams(0.25, 0.0, 0.25, 70);

  Serial.println("Sistema inicializado - Iniciando calibração...");
  
  // Calibração automática
  calibrateSensors();
  
  Serial.println("Calibração completa - Aguardando linha...");
  stateMachine.setState(STATE_INITIAL);
}

void loop() {
  perfMonitor.startLoop();

  // Atualizar sensores
  sensorManager.updateReadings();
  updateStatusData();

  // Executar lógica principal
  executeSimpleStateMachine();

  // Mostrar status periodicamente
  static unsigned long lastStatus = 0;
  if (millis() - lastStatus >= 2000) {
    printSimpleStatus();
    lastStatus = millis();
  }

  perfMonitor.endLoop();
}

void initializeStatusData() {
  statusData.currentState = STATE_CALIBRATION;
  statusData.stateString = "CALIBRAÇÃO";
  statusData.error = 0;
  statusData.pidValue = 0;
  statusData.leftSpeed = 0;
  statusData.rightSpeed = 0;
  statusData.transitionCount = 0;
  statusData.stopTransitionCount = 0;
  statusData.uptime = 0;
  statusData.loopFrequency = 0;
  statusData.wifiActive = false;
  statusData.userConnected = false;
}

void calibrateSensors() {
  Serial.println("Mova o robô sobre a linha durante a calibração...");
  digitalWrite(LED_BUILTIN, HIGH);
  
  for (int i = 0; i < 400; i++) {
    sensorManager.updateReadings();
    delay(10);
    
    if (i % 100 == 0) {
      Serial.print("Progresso: ");
      Serial.print((i * 100) / 400);
      Serial.println("%");
    }
  }
  
  digitalWrite(LED_BUILTIN, LOW);
  Serial.println("Calibração concluída!");
}

void updateStatusData() {
  statusData.currentState = stateMachine.getCurrentState();
  statusData.stateString = stateMachine.getStateString();
  statusData.error = pidController.getError();
  statusData.pidValue = pidController.getPIDValue();
  statusData.leftSpeed = pidController.getLeftSpeed();
  statusData.rightSpeed = pidController.getRightSpeed();
  statusData.uptime = perfMonitor.getUptime();
  statusData.loopFrequency = perfMonitor.getLoopFrequency();
}

void executeSimpleStateMachine() {
  RobotState currentState = stateMachine.getCurrentState();
  
  switch (currentState) {
    case STATE_INITIAL:
      handleInitialState();
      break;
      
    case STATE_STRAIGHT:
      handleRunningState();
      break;
      
    case STATE_STOPPED:
      handleStoppedState();
      break;
      
    default:
      // Estados não utilizados neste exemplo
      break;
  }
}

void handleInitialState() {
  // Verificar se detectou linha
  uint16_t linePosition = sensorManager.getLinePosition();
  
  // Se a posição não for 0, significa que detectou a linha
  if (linePosition > 100 && linePosition < 6900) {
    stateMachine.setState(STATE_STRAIGHT);
    pidController.setCurrentState(STATE_STRAIGHT);
    pidController.reset();
    Serial.println("Linha detectada - Iniciando seguimento");
  }
}

void handleRunningState() {
  // Calcular erro da linha
  uint16_t linePosition = sensorManager.getLinePosition();
  int error = 3500 - linePosition; // Centro da linha
  
  // Verificar se perdeu a linha (parar se necessário)
  if (linePosition == 0 || linePosition == 7000) {
    static unsigned long lostLineTime = 0;
    if (lostLineTime == 0) {
      lostLineTime = millis();
    } else if (millis() - lostLineTime > 1000) {
      // Perdeu a linha por mais de 1 segundo
      stateMachine.setState(STATE_STOPPED);
      Serial.println("Linha perdida - Parando");
      return;
    }
  // } else {
    // lostLineTime = 0; // Reset timer se encontrou a linha
  // }
  
  // Calcular PID e controlar motores
  pidController.calculate(error);
  motorController.setMotorSpeeds(pidController.getLeftSpeed(), pidController.getRightSpeed());
}

void handleStoppedState() {
  motorController.stopMotors();
  pidController.reset();
  
  // LED fixo para indicar parada
  digitalWrite(LED_BUILTIN, HIGH);
  
  // Reiniciar se detectar linha novamente
  uint16_t linePosition = sensorManager.getLinePosition();
  if (linePosition > 100 && linePosition < 6900) {
    stateMachine.setState(STATE_INITIAL);
    digitalWrite(LED_BUILTIN, LOW);
    Serial.println("Linha detectada novamente - Reiniciando");
  }
}

void printSimpleStatus() {
  Serial.println("=== STATUS SIMPLES ===");
  Serial.println("Estado: " + statusData.stateString);
  Serial.println("Posição: " + String(sensorManager.getLinePosition()));
  Serial.println("Erro: " + String(statusData.error));
  Serial.println("PID: " + String(statusData.pidValue));
  Serial.println("Motores - E:" + String(statusData.leftSpeed) + " D:" + String(statusData.rightSpeed));
  Serial.println("Frequência: " + String(statusData.loopFrequency, 1) + " Hz");
  Serial.println("Uptime: " + String(statusData.uptime/1000) + "s");
  Serial.println("=====================");
}
