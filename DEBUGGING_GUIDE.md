# Guia de Depuração - Sistema Modular

## Problemas Comuns e Soluções

### 1. Erros de Compilação

#### Problema: "identifier 'uint8_t' is undefined"
**Causa**: Falta de inclusão do Arduino.h
**Solução**: 
- Verifique se todos os arquivos .h incluem `#include <Arduino.h>`
- Certifique-se de que está usando o board package correto

#### Problema: "RobotConfig.h not found"
**Causa**: Arquivo em local incorreto
**Solução**:
- Todos os arquivos devem estar na mesma pasta do .ino principal
- NÃO coloque arquivos na pasta libraries do Arduino

#### Problema: "QTRSensors.h not found"
**Causa**: Biblioteca não instalada
**Solução**:
- Instale via Library Manager: Tools > Manage Libraries > "QTRSensors"
- Ou baixe de: https://github.com/pololu/qtr-sensors-arduino

### 2. Estrutura de Arquivos Correta

```
Projeto/
├── Assemble_2PID_V2_NoArduinoJson.ino  # Arquivo principal
├── RobotConfig.h                        # Configurações
├── SensorManager.h                      # Headers
├── SensorManager.cpp                    # Implementações
├── MotorController.h
├── MotorController.cpp
├── PIDController.h
├── PIDController.cpp
├── StateMachine.h
├── StateMachine.cpp
├── WiFiManager.h
├── WiFiManager.cpp
├── WebServer.h
├── WebServer.cpp
├── PerformanceMonitor.h
├── PerformanceMonitor.cpp
└── CompilationTest.ino                  # Teste de compilação
```

### 3. Configuração do Arduino IDE

#### Board Settings:
- **Board**: "Raspberry Pi Pico W"
- **CPU Speed**: 133 MHz
- **Flash Size**: 2MB (Sketch: 1MB, FS: 1MB)
- **Debug Port**: Disabled
- **USB Stack**: Pico SDK

#### Bibliotecas Necessárias:
- QTRSensors (by Pololu)
- WiFi (incluída no board package)

### 4. Teste de Compilação

Use o arquivo `CompilationTest.ino` para verificar se tudo compila:

1. Abra `CompilationTest.ino`
2. Compile (Ctrl+R)
3. Se compilar sem erros, o sistema está OK
4. Se houver erros, siga as soluções abaixo

### 5. Erros Específicos e Soluções

#### "Multiple definition of..."
**Causa**: Inclusão duplicada de arquivos
**Solução**: 
- Verifique se não há arquivos duplicados
- Use guards (#ifndef/#define/#endif) em todos os .h

#### "undefined reference to..."
**Causa**: Implementação faltando ou não linkada
**Solução**:
- Verifique se todos os .cpp estão na pasta
- Confirme que os métodos estão implementados

#### WiFi não conecta
**Causa**: Configuração incorreta
**Solução**:
- Verifique SSID e senha em RobotConfig.h
- Use rede 2.4GHz (Pico W não suporta 5GHz)
- Teste com hotspot do celular

### 6. Depuração Passo a Passo

#### Passo 1: Teste Básico
```cpp
// Teste apenas com Serial
void setup() {
  Serial.begin(115200);
  Serial.println("Sistema iniciado");
}
void loop() {
  Serial.println("Loop funcionando");
  delay(1000);
}
```

#### Passo 2: Teste dos Sensores
```cpp
#include "RobotConfig.h"
#include "SensorManager.h"

SensorManager sensorManager;

void setup() {
  Serial.begin(115200);
  sensorManager.begin();
}

void loop() {
  sensorManager.updateReadings();
  Serial.println("Posição: " + String(sensorManager.getLinePosition()));
  delay(100);
}
```

#### Passo 3: Teste dos Motores
```cpp
#include "RobotConfig.h"
#include "MotorController.h"

MotorController motorController;

void setup() {
  Serial.begin(115200);
  motorController.begin();
  motorController.testMotors();
}

void loop() {
  // Teste completo
}
```

### 7. Comandos de Depuração

Quando o sistema estiver funcionando, use estes comandos no Serial Monitor:

- `status` - Status completo do sistema
- `test` - Teste de todos os módulos
- `calibrate` - Iniciar calibração
- `help` - Lista de comandos

### 8. Verificação de Hardware

#### Conexões dos Sensores QTR:
- Pinos 1-8: Sensores QTR
- Pino 0: Emitter
- VCC: 3.3V ou 5V
- GND: Ground

#### Conexões dos Motores:
- Pino 15: EN_A (Motor A)
- Pino 14: IN1_A (Motor A)
- Pino 17: EN_B (Motor B)
- Pino 16: IN1_B (Motor B)

#### Sensores de Estado:
- Pino 26: Sensor 1
- Pino 27: Sensor 2

### 9. Problemas de Performance

#### Loop muito lento:
- Desative WiFi durante operação
- Reduza delay() nos loops
- Use processamento assíncrono

#### Memória insuficiente:
- Reduza tamanho das strings
- Use PROGMEM para constantes
- Otimize estruturas de dados

### 10. Logs de Depuração

Adicione estas linhas para debug:

```cpp
#define DEBUG 1

#if DEBUG
  #define DEBUG_PRINT(x) Serial.print(x)
  #define DEBUG_PRINTLN(x) Serial.println(x)
#else
  #define DEBUG_PRINT(x)
  #define DEBUG_PRINTLN(x)
#endif
```

### 11. Contato para Suporte

Se os problemas persistirem:

1. Verifique se seguiu todos os passos do INSTALLATION.md
2. Use o CompilationTest.ino para isolar problemas
3. Documente o erro exato e as configurações usadas
4. Verifique a versão do board package (recomendado: mais recente)

### 12. Checklist de Verificação

- [ ] Board package instalado corretamente
- [ ] Biblioteca QTRSensors instalada
- [ ] Todos os arquivos na mesma pasta
- [ ] RobotConfig.h configurado corretamente
- [ ] Hardware conectado conforme especificação
- [ ] CompilationTest.ino compila sem erros
- [ ] Serial Monitor configurado para 115200 baud
