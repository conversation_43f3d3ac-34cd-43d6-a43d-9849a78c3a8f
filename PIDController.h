#ifndef PID_CONTROLLER_H
#define PID_CONTROLLER_H

#include "RobotConfig.h"

// ============================================================================
// CONTROLADOR PID DUAL (RETA/CURVA) PARA SEGUIMENTO DE LINHA
// ============================================================================

class PIDController {
private:
  // Parâmetros PID para reta e curva
  PIDParams straightParams;
  PIDParams curveParams;
  
  // Variáveis PID
  int P, I, D;
  int previousError;
  int pidValue;
  int error;
  
  // Velocidades calculadas
  int leftSpeed;
  int rightSpeed;
  
  // Estado atual
  RobotState currentState;

public:
  PIDController();
  
  // Inicialização
  void begin();
  void reset();
  
  // Configuração de parâmetros
  void setStraightParams(float kp, float ki, float kd, int speed);
  void setCurveParams(float kp, float ki, float kd, int speed);
  void setCurrentState(RobotState state);
  
  // Cálculo PID
  void calculate(int lineError);
  
  // Acesso aos resultados
  int getLeftSpeed();
  int getRightSpeed();
  int getPIDValue();
  int getError();
  
  // Acesso aos parâmetros
  PIDParams getStraightParams();
  PIDParams getCurveParams();
  PIDParams getCurrentParams();
  
  // Utilitários
  void printPIDStatus();
  void printParameters();
};

#endif // PID_CONTROLLER_H
