#ifndef WIFI_MANAGER_H
#define WIFI_MANAGER_H

#include <Arduino.h>
#include <WiFi.h>
#include "RobotConfig.h"

// ============================================================================
// GERENCIADOR WiFi PARA RASPBERRY PI PICO W (MODO CLIENTE)
// ============================================================================

class WiFiManager {
private:
  bool wifiActive;
  bool userConnected;
  unsigned long lastWiFiCheck;
  int reconnectAttempts;
  
  // Métodos privados
  void printWiFiStatus(wl_status_t status);
  bool attemptConnection();

public:
  WiFiManager();
  
  // Inicialização e controle
  void begin();
  bool connect();
  void disconnect();
  void monitor();
  
  // Status
  bool isActive();
  bool isConnected();
  bool hasUserConnected();
  void setUserConnected(bool connected);
  
  // Informações de rede
  String getSSID();
  String getLocalIP();
  String getGatewayIP();
  String getDNSIP();
  String getMACAddress();
  int getRSSI();
  
  // Utilitários
  void scanNetworks();
  void printConnectionInfo();
  void printNetworkInfo();
};

#endif // WIFI_MANAGER_H
